<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>gov.chinatax.apaas</groupId>
        <artifactId>bittalk-training-service</artifactId>
        <version>1.0.0</version>
    </parent>

    <groupId>gov.chinatax.apaas</groupId>
    <artifactId>bittalk-training-service-api</artifactId>
    <version>1.0.0</version>
    <name>bittalk-training-service-api</name>
    <description>bittalk-training-service-api</description>

    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <deploy.releaseRepository.url>http://gd-nexus.oa.servyou-it.com/nexus/content/repositories/geshui/</deploy.releaseRepository.url>
        <deploy.snapshotRepository.url>http://gd-nexus.oa.servyou-it.com/nexus/content/repositories/geshui-snapshot/</deploy.snapshotRepository.url>
    </properties>

    <dependencies>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>template-module-common</artifactId>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>geshui</id>
            <name>geshui</name>
            <url>${deploy.releaseRepository.url}</url>
        </repository>
        <snapshotRepository>
            <id>geshui-snapshot</id>
            <name>geshui-snapshot</name>
            <url>${deploy.snapshotRepository.url}</url>
        </snapshotRepository>
    </distributionManagement>

</project>
