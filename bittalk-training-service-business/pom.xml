<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>gov.chinatax.apaas</groupId>
        <artifactId>bittalk-training-service</artifactId>
        <version>1.0.0</version>
    </parent>

    <groupId>gov.chinatax.apaas</groupId>
    <artifactId>bittalk-training-service-business</artifactId>
    <version>1.0.0</version>
    <name>bittalk-training-service-business</name>
    <description>bittalk-training-service-business</description>

    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <deploy.releaseRepository.url>http://gd-nexus.oa.servyou-it.com/nexus/content/repositories/geshui/</deploy.releaseRepository.url>
        <deploy.snapshotRepository.url>http://gd-nexus.oa.servyou-it.com/nexus/content/repositories/geshui-snapshot/</deploy.snapshotRepository.url>
    </properties>

    <dependencies>
        <!-- api -->
        <dependency>
            <groupId>gov.chinatax.apaas</groupId>
            <artifactId>bittalk-training-service-api</artifactId>
        </dependency>
        <!-- commons -->
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>commons-tool</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>commons-log-logback</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>commons-checkup</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>commons-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>commons-web</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>commons-message</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>commons-session</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>commons-oauth</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>commons-validate</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>commons-serialize</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>commons-database</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>commons-idgen-sequence</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>commons-high-availability-sentinel</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>commons-high-availability-sentinel-config</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>commons-gst-all</artifactId>
        </dependency>
        <!-- middleware -->
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>common-middleware-api</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>common-middleware-cache-core</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>common-middleware-configuration-core</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>common-middleware-mq-core</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>common-middleware-sse-core</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>common-middleware-os-core</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>common-middleware-scheduler-core</artifactId>
        </dependency>
        <!-- template -->
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>template-module-core</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>template-module-support</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>template-module-repository</artifactId>
        </dependency>
        <!-- other -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>geshui</id>
            <name>geshui</name>
            <url>${deploy.releaseRepository.url}</url>
        </repository>
        <snapshotRepository>
            <id>geshui-snapshot</id>
            <name>geshui-snapshot</name>
            <url>${deploy.snapshotRepository.url}</url>
        </snapshotRepository>
    </distributionManagement>

</project>
