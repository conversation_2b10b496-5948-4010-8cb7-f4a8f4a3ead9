package gov.chinatax.apaas.training.application;

import gov.chinatax.apaas.training.domain.model.Demo;
import gov.chinatax.apaas.training.domain.service.DemoService;
import gov.chinatax.apaas.training.application.converter.DemoDTOConverter;
import gov.chinatax.apaas.training.pojo.dto.DemoDTO;
import gov.chinatax.framework.template.common.pager.PageInfo;
import gov.chinatax.framework.template.common.pager.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * '各种业务组合服务示例'
 *
 * <AUTHOR>
 */
@Component
public class DemoManager {

    @Autowired
    DemoService demoService;

    @Autowired
    DemoDTOConverter converter;

    public DemoDTO queryUser(String userName) {
        DemoDTO demoDTO = new DemoDTO();
        if (StringUtils.isNotBlank(userName)) {
            List<Demo> demoList = demoService.getAll();
            for (Demo demo : demoList) {
                if (userName.equalsIgnoreCase(demo.getUserName())) {
                    demoDTO = converter.ConvertDemoToDemoDTO(demo);
                    break;
                }
            }
        }
        return demoDTO;
    }

    public DemoDTO queryUser(String userName, String password) {
        DemoDTO demoDTO = new DemoDTO();
        if (StringUtils.isNotBlank(userName)) {
            Demo demo = demoService.queryUser(userName, password);
            demoDTO = converter.ConvertDemoToDemoDTO(demo);
        }
        return demoDTO;
    }

    public List<DemoDTO> getAll() {
        List<DemoDTO> demoDTOList = new ArrayList<DemoDTO>();
        List<Demo> demoList = demoService.getAll();
        if (demoList != null && demoList.size() > 0) {
            demoDTOList = converter.ConvertDemoToDemoDTO(demoList);
        }
        return demoDTOList;
    }

    public PageResult<DemoDTO> getByPage(PageInfo pageInfo, DemoDTO demoDTO) {
        Demo demo = new Demo();
        demo.setAge(demoDTO.getAge());
        PageResult<Demo> pageResult = demoService.getByPage(pageInfo, demo);
        List<DemoDTO> demoDTOList = converter.ConvertDemoToDemoDTO(pageResult.getRows());
        return new PageResult<DemoDTO>(pageResult.getTotal(), demoDTOList);
    }

    public DemoDTO getById(Long id) {
        Demo demo = demoService.getById(id);
        DemoDTO demoDTO = converter.ConvertDemoToDemoDTO(demo);
        return demoDTO;
    }

    public void add(DemoDTO demoDTO) {
        Demo demo = converter.ConvertDemoDTOToDemo(demoDTO);
        demoService.add(demo);
    }

    public void removeById(Long id) {
        demoService.removeById(id);
    }

    public void editById(DemoDTO demoDTO) {
        Demo demo = converter.ConvertDemoDTOToDemo(demoDTO);
        demoService.editById(demo);
    }
}
