package gov.chinatax.apaas.training.application.converter;

import gov.chinatax.apaas.training.domain.model.Demo;
import gov.chinatax.apaas.training.pojo.dto.DemoDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface DemoDTOConverter {

	List<DemoDTO> ConvertDemoToDemoDTO(List<Demo> demoList);

	DemoDTO ConvertDemoToDemoDTO(Demo demo);

	Demo ConvertDemoDTOToDemo(DemoDTO demoDTO);

}
