package gov.chinatax.apaas.training.domain.converter;

import gov.chinatax.apaas.training.domain.model.Demo;
import gov.chinatax.apaas.training.infrastructure.db.dataobject.User;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface DemoConverter {

	List<Demo> ConvertUserToDemo(List<User> userList);

	Demo ConvertUserToDemo(User user);

	User ConvertDemoToUser(Demo demo);

}
