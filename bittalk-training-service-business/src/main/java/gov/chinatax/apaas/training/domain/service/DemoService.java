package gov.chinatax.apaas.training.domain.service;

import gov.chinatax.apaas.training.domain.model.Demo;
import gov.chinatax.framework.template.common.pager.PageInfo;
import gov.chinatax.framework.template.common.pager.PageResult;

import java.util.List;

/**
 * '业务服务定义示例'
 *
 * <AUTHOR>
 */
public interface DemoService {

    List<Demo> getAll();

    PageResult<Demo> getByPage(PageInfo pageInfo, Demo demo);

    Demo queryUser(String userName, String password);

    Demo getById(Long id);

    void add(Demo demo);

    void removeById(Long id);

    void editById(Demo demo);

}
