package gov.chinatax.apaas.training.domain.service.impl;

import gov.chinatax.apaas.training.domain.converter.DemoConverter;
import gov.chinatax.apaas.training.domain.model.Demo;
import gov.chinatax.apaas.training.domain.service.DemoService;
import gov.chinatax.apaas.training.infrastructure.db.dao.UserDao;
import gov.chinatax.apaas.training.infrastructure.db.dataobject.User;
import gov.chinatax.framework.template.common.pager.PageInfo;
import gov.chinatax.framework.template.common.pager.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * '业务服务实现示例'
 *
 * <AUTHOR>
 */
@Service
public class DemoServiceImpl implements DemoService {

    @Autowired
    UserDao userDao;

    @Autowired
    DemoConverter converter;

    @Override
    public List<Demo> getAll() {
        List<User> users = userDao.getAll();
        List<Demo> demoList = converter.ConvertUserToDemo(users);
        return demoList;
    }

    @Override
    public PageResult<Demo> getByPage(PageInfo pageInfo, Demo demo) {
        PageResult<User> pageResult = userDao.find(pageInfo, converter.ConvertDemoToUser(demo));
        List<Demo> demoList = converter.ConvertUserToDemo(pageResult.getRows());
        return new PageResult<Demo>(pageResult.getTotal(), demoList);
    }

    @Override
    public Demo queryUser(String userName, String password) {
        User user = userDao.findUser(userName, password);
        Demo demo = converter.ConvertUserToDemo(user);
        return demo;
    }

    @Override
    public Demo getById(Long id) {
        User user = userDao.getById(id);
        Demo demo = converter.ConvertUserToDemo(user);
        return demo;
    }

    @Override
    public void add(Demo demo) {
        User user = converter.ConvertDemoToUser(demo);
        userDao.add(user);
    }

    @Override
    public void removeById(Long id) {
        userDao.removeById(id);
    }

    @Override
    public void editById(Demo demo) {
        User user = converter.ConvertDemoToUser(demo);
        userDao.editById(user);
    }

}
