package gov.chinatax.apaas.training.infrastructure.cache;

import gov.chinatax.framework.middleware.cache.api.ICacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * '缓存客户端示例'
 *
 * <AUTHOR>
 */
@Component
public class CacheClient {

    @Autowired(required = false)
    ICacheService cacheService;

    public Object get(String key) {
        return cacheService.get(key);
    }

    public boolean set(String key, String value) {
        return cacheService.set(key, value);
    }

    public void del(String key) {
        cacheService.del(key);
    }
}
