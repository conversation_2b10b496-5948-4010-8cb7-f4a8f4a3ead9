package gov.chinatax.apaas.training.infrastructure.db.dao;

import gov.chinatax.apaas.training.infrastructure.db.dataobject.User;
import gov.chinatax.apaas.training.infrastructure.db.dataobject.example.UserExample;
import gov.chinatax.framework.template.common.pager.PageInfo;
import gov.chinatax.framework.template.common.pager.PageResult;
import gov.chinatax.framework.template.repository.dao.CrudDao;

public interface UserDao extends Crud<PERSON>ao<User, UserExample, Long> {

    PageResult<User> find(PageInfo pageInfo, User user);

    User findUser(String userName, String password);

}