package gov.chinatax.apaas.training.infrastructure.db.dao.impl;

import gov.chinatax.apaas.training.infrastructure.db.dao.UserDao;
import gov.chinatax.apaas.training.infrastructure.db.dataobject.User;
import gov.chinatax.apaas.training.infrastructure.db.dataobject.example.UserExample;
import gov.chinatax.apaas.training.infrastructure.db.mapper.UserMapper;
import gov.chinatax.framework.template.common.pager.PageInfo;
import gov.chinatax.framework.template.common.pager.PageResult;
import gov.chinatax.framework.template.repository.dao.AbstractCrudDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("UserDao")
public class UserDaoImpl extends AbstractCrudDao<UserMapper, User, UserExample, Long>
        implements UserDao {

    @Override
    public PageResult<User> find(PageInfo pageInfo, User user) {
        UserExample example = new UserExample();
        UserExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(user.getUserName())) {
            criteria.andUserNameEqualTo(user.getUserName());
        }
        if (StringUtils.isNotBlank(user.getName())) {
            criteria.andNameEqualTo(user.getName());
        }
        if (user.getAge() != null) {
            criteria.andAgeEqualTo(user.getAge());
        }
        if (StringUtils.isNotBlank(user.getEmail())) {
            criteria.andEmailEqualTo(user.getEmail());
        }
        return this.getByPage(pageInfo, example);
    }

    @Override
    public User findUser(String userName, String password) {
        UserExample example = new UserExample();
        if (StringUtils.isNotBlank(userName) && StringUtils.isNotBlank(password)) {
            example.createCriteria()
                    .andUserNameEqualTo(userName)
                    .andPasswordEqualTo(password);
            List<User> userList = this.getByExample(example);
            if (userList != null && userList.size() > 0) {
                return userList.get(0);
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

}