package gov.chinatax.apaas.training.infrastructure.proxy;

import gov.chinatax.apaas.training.api.DemoFacadeService;
import gov.chinatax.apaas.training.pojo.dto.DemoDTO;
import gov.chinatax.framework.middleware.sse.core.config.annotation.SseReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * '远程服务代理示例'
 *
 * <AUTHOR>
 */
@Lazy
@Component
public class RPCClient {

    @SseReference(version = "2.0.1", group = "servyou", providerName = "bittalk-training-service", check = false)
    private DemoFacadeService demoFacadeService;

    public DemoDTO queryUser(String userName) {
        return demoFacadeService.queryUser(userName);
    }
}
