package gov.chinatax.apaas.training.interfaces.controller;

import gov.chinatax.apaas.training.infrastructure.cache.CacheClient;
import gov.chinatax.framework.common.standard.util.RespGenerator;
import gov.chinatax.framework.template.support.controller.BaseController;
import gov.chinatax.its.common.standard.api.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * '缓存示例demo'
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/cache")
public class CacheController extends BaseController {

    @Autowired
    CacheClient client;

    @GetMapping(value = "/get")
    public ApiResponse<Object> get(String key) {
        return RespGenerator.successful(client.get(key));
    }

    @GetMapping(value = "/set")
    public ApiResponse<Object> set(String key, String value) {
        return RespGenerator.successful(client.set(key, value));
    }

    @GetMapping(value = "/del")
    public ApiResponse<Object> del(String key) {
        client.del(key);
        return RespGenerator.successful();
    }

}
