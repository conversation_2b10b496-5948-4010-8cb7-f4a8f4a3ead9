package gov.chinatax.apaas.training.interfaces.controller;

import gov.chinatax.apaas.training.application.DemoManager;
import gov.chinatax.apaas.training.interfaces.converter.DemoVOConverter;
import gov.chinatax.apaas.training.pojo.dto.DemoDTO;
import gov.chinatax.apaas.training.pojo.vo.DemoVO;
import gov.chinatax.apaas.training.pojo.vo.ReqDemoVO;
import gov.chinatax.apaas.training.pojo.vo.RespDemoVO;
import gov.chinatax.framework.common.standard.util.RespGenerator;
import gov.chinatax.framework.template.common.pager.PageInfo;
import gov.chinatax.framework.template.common.pager.PageResult;
import gov.chinatax.framework.template.support.controller.BaseController;
import gov.chinatax.framework.template.support.vo.DataGridPagerVO;
import gov.chinatax.its.common.standard.api.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * '数据库示例demo'
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/demo")
public class DemoController extends BaseController {

    @Autowired
    DemoManager demoManager;

    @Autowired
    DemoVOConverter converter;

    @GetMapping(value = "/list")
    public ApiResponse<RespDemoVO> list() {
        List<DemoDTO> demoDTOList = demoManager.getAll();
        RespDemoVO respDemoVO = new RespDemoVO();
        List<DemoVO> demoVOList = converter.ConvertDemoDTOToDemoVO(demoDTOList);
        respDemoVO.setList(demoVOList);
        return RespGenerator.successful(respDemoVO);
    }

    @PostMapping(value = "/pageList")
    public ApiResponse<PageResult<DemoVO>> getByPage(@RequestBody ReqDemoVO reqDemoVO) {
        DataGridPagerVO pager = new DataGridPagerVO();
        if (reqDemoVO != null && reqDemoVO.getPageParam() != null) {
            pager.setPage(reqDemoVO.getPageParam().getPage());
            pager.setRows(reqDemoVO.getPageParam().getRows());
        }
        PageInfo pageInfo = pager.toPageInfo();
        DemoDTO demoDTO = new DemoDTO();
        if (reqDemoVO != null && reqDemoVO.getAge() != null) {
            demoDTO.setAge(reqDemoVO.getAge());
        }
        PageResult<DemoDTO> pageResult = demoManager.getByPage(pageInfo, demoDTO);
        List<DemoVO> demoVOList = converter.ConvertDemoDTOToDemoVO(pageResult.getRows());
        return RespGenerator
                .successful(new PageResult<DemoVO>(pageResult.getTotal(), demoVOList));
    }

    @PostMapping(value = "/getDemo")
    public ApiResponse<DemoVO> getDemo(@RequestBody ReqDemoVO reqDemoVO) {
        DemoDTO demoDTO = demoManager.getById(reqDemoVO.getId());
        DemoVO demoVO = converter.ConvertDemoDTOToDemoVO(demoDTO);
        return RespGenerator.successful(demoVO);
    }

    @PostMapping(value = "/add")
    public ApiResponse<?> add(@RequestBody ReqDemoVO reqDemoVO) {
        DemoDTO demoDTO = converter.ConvertReqDemoVOToDemoDTO(reqDemoVO);
        demoManager.add(demoDTO);
        return RespGenerator.successful();
    }

    @PostMapping(value = "/remove")
    public ApiResponse<?> remove(@RequestBody ReqDemoVO reqDemoVO) {
        demoManager.removeById(reqDemoVO.getId());
        return RespGenerator.successful();
    }

    @PostMapping(value = "/edit")
    public ApiResponse<DemoDTO> edit(@RequestBody ReqDemoVO reqDemoVO) {
        DemoDTO demoDTO = converter.ConvertReqDemoVOToDemoDTO(reqDemoVO);
        demoManager.editById(demoDTO);
        return RespGenerator.successful();
    }

}
