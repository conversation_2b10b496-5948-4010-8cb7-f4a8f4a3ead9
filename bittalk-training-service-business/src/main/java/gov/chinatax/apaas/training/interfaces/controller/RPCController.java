package gov.chinatax.apaas.training.interfaces.controller;

import gov.chinatax.apaas.training.infrastructure.proxy.RPCClient;
import gov.chinatax.apaas.training.pojo.dto.DemoDTO;
import gov.chinatax.framework.common.standard.util.RespGenerator;
import gov.chinatax.framework.template.support.controller.BaseController;
import gov.chinatax.its.common.standard.api.ApiResponse;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * '远程调用示例demo'
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/rpc")
public class RPCController extends BaseController {

    @Autowired
    ObjectProvider<RPCClient> objectProvider;

    @GetMapping(value = "/queryUser")
    public ApiResponse<DemoDTO> queryUser(String userName) {
        DemoDTO demoDTO = objectProvider.getIfAvailable().queryUser(userName);
        return RespGenerator.successful(demoDTO);
    }

}
