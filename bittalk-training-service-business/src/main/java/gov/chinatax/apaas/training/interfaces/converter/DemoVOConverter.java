package gov.chinatax.apaas.training.interfaces.converter;

import gov.chinatax.apaas.training.pojo.dto.DemoDTO;
import gov.chinatax.apaas.training.pojo.vo.DemoVO;
import gov.chinatax.apaas.training.pojo.vo.ReqDemoVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface DemoVOConverter {

    List<DemoVO> ConvertDemoDTOToDemoVO(List<DemoDTO> demoDTOList);

    DemoVO ConvertDemoDTOToDemoVO(DemoDTO demoDTO);

    DemoDTO ConvertReqDemoVOToDemoDTO(ReqDemoVO reqDemoVO);

}
