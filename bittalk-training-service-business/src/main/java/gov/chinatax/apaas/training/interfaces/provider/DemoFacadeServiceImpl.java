package gov.chinatax.apaas.training.interfaces.provider;

import gov.chinatax.apaas.training.application.DemoManager;
import gov.chinatax.apaas.training.api.DemoFacadeService;
import gov.chinatax.apaas.training.pojo.dto.DemoDTO;
import gov.chinatax.framework.middleware.sse.core.config.annotation.SseService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * '远程服务示例'
 *
 * <AUTHOR>
 */
@SseService(version = "2.0.1", group = "servyou")
public class DemoFacadeServiceImpl implements DemoFacadeService {

    @Autowired
    DemoManager demoManager;

    @Override
    public DemoDTO queryUser(String userName) {
        return demoManager.queryUser(userName);
    }

}
