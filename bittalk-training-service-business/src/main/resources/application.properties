spring.application.name=training_service${its.deploy.name:}
servyou.web.enable=false
servyou.highavailability.enable=false
#################################### common config : ####################################
servyou.config.application.name=bittalk-training-service
servyou.config.external.enable=false
servyou.config.names=["bittalk-training.override","bittalk-training.logback"]
server.servlet.context-path=/training/web


#servyou.config.open=nacos
#nacos.config.server-addr=nacos-test-apaas.dc.servyou-it.com:8851
#nacos.config.namespace=xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
#nacos.config.username=nacos
#nacos.config.password=iVV4CCpphb/0xab957mrLQ==
#nacos.config.password.encryption=true