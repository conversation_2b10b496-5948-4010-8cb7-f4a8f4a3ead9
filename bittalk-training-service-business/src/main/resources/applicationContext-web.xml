<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.1.xsd
        http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-4.1.xsd">

    <!--所有swagger目录的访问，直接访问location指定的目录 -->
    <mvc:resources
            location="classpath:/META-INF/resources/swagger-ui.html"
            mapping="/swagger-ui.html"/>
    <mvc:resources
            location="classpath:/META-INF/resources/webjars/"
            mapping="/webjars/**"/>

    <mvc:interceptors>
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <mvc:exclude-mapping path="/swagger/**"/>
            <mvc:exclude-mapping path="/v2/**"/>
            <bean class="gov.chinatax.framework.common.web.interceptor.LogInterceptor"/>
        </mvc:interceptor>
    </mvc:interceptors>

    <mvc:annotation-driven content-negotiation-manager="contentNegotiationManager">
        <mvc:message-converters register-defaults="true">
            <bean class="gov.chinatax.framework.common.web.converter.MappingJackson2HttpMessageConverter">
                <property name="prettyPrint" value="true"/>
                <property name="messagei18n" ref="messagei18n"/>
            </bean>
            <bean class="org.springframework.http.converter.StringHttpMessageConverter">
                <constructor-arg value="UTF-8"/>
            </bean>
        </mvc:message-converters>
    </mvc:annotation-driven>

    <mvc:default-servlet-handler/>

    <bean id="contentNegotiationManager" class="org.springframework.web.accept.ContentNegotiationManagerFactoryBean">
        <property name="ignoreAcceptHeader" value="true"/>
        <property name="defaultContentType" value="application/json"/>
        <property name="mediaTypes">
            <value>
                json=application/json
                xml=application/xml
            </value>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetClass" value="java.lang.System"/>
        <property name="targetMethod" value="setProperty"/>
        <property name="arguments">
            <list>
                <value>file.encoding</value>
                <value>UTF-8</value>
            </list>
        </property>
    </bean>

</beans>
