<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>[%-5p][%d{yyyy/MM/dd HH:mm:ss.SSS}][%.-32t][rid:%mdc{requestid},sid:%mdc{spanid},uid:%mdc{userid},tid:%mdc{traceid},swjg:%mdc{swjgDm}] %m [%.50logger]%ex%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="common" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>apaas/logs/common.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>apaas/logs/common.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>180</maxHistory>
        </rollingPolicy>
        <append>true</append>
        <encoder>
            <pattern>[%-5p][%d{yyyy/MM/dd HH:mm:ss.SSS}][%.-32t][rid:%mdc{requestid},sid:%mdc{spanid},uid:%mdc{userid},tid:%mdc{traceid},swjg:%mdc{swjgDm}] %m [%.50logger]%rEx{3}%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator class="gov.chinatax.framework.common.log.mdc.MDCStressTestEvaluator"/>
            <OnMatch>DENY</OnMatch>
            <OnMismatch>ACCEPT</OnMismatch>
        </filter>
    </appender>

    <appender name="error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>apaas/logs/error.log</file>
        <append>true</append>
        <encoder>
            <pattern>[%-5p][%d{yyyy/MM/dd HH:mm:ss.SSS}][%t][rid:%mdc{requestid},sid:%mdc{spanid},uid:%mdc{userid},tid:%mdc{traceid},swjg:%mdc{swjgDm}] %m [%logger]%ex%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>error</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>apaas/logs/error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>180</maxHistory>
        </rollingPolicy>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator class="gov.chinatax.framework.common.log.mdc.MDCStressTestEvaluator"/>
            <OnMatch>DENY</OnMatch>
            <OnMismatch>ACCEPT</OnMismatch>
        </filter>
    </appender>

    <appender name="apaas.monitor" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>apaas/logs/monitor.log</file>
        <append>true</append>
        <encoder>
            <pattern>[%-5p][%d{yyyy/MM/dd HH:mm:ss.SSS}][%.-32t][rid:%mdc{requestid},sid:%mdc{spanid},uid:%mdc{userid},tid:%mdc{traceid},swjg:%mdc{swjgDm}] %m [%.50logger]%ex%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>apaas/logs/monitor.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>180</maxHistory>
        </rollingPolicy>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator class="gov.chinatax.framework.common.log.mdc.MDCStressTestEvaluator"/>
            <OnMatch>DENY</OnMatch>
            <OnMismatch>ACCEPT</OnMismatch>
        </filter>
    </appender>

    <appender name="apaas.sql" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>apaas/logs/sql.log</file>
        <append>true</append>
        <encoder>
            <pattern>[%-5p][%d{yyyy/MM/dd HH:mm:ss.SSS}][%.-32t][rid:%mdc{requestid},sid:%mdc{spanid},uid:%mdc{userid},tid:%mdc{traceid},swjg:%mdc{swjgDm}] %m [%.50logger]%ex%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>apaas/logs/sql.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>180</maxHistory>
        </rollingPolicy>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator class="gov.chinatax.framework.common.log.mdc.MDCStressTestEvaluator"/>
            <OnMatch>DENY</OnMatch>
            <OnMismatch>ACCEPT</OnMismatch>
        </filter>
    </appender>

    <logger name="monitor" level="info">
        <appender-ref ref="apaas.monitor" />
    </logger>

    <logger name="parameterLog" level="debug"/>

    <logger name="mapper" level="DEBUG" additivity="false">
        <appender-ref ref="apaas.sql" />
        <appender-ref ref="console"/>
    </logger>

    <root level="info">
        <appender-ref ref="common"/>
        <appender-ref ref="console"/>
        <appender-ref ref="error"/>
    </root>
</configuration>