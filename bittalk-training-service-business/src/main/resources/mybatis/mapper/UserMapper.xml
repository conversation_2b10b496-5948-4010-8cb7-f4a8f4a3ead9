<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="gov.chinatax.apaas.training.infrastructure.db.mapper.UserMapper">
    <resultMap id="UserMap" type="gov.chinatax.apaas.training.infrastructure.db.dataobject.User">
			<id column="ID" property="id" />
			<result column="USER_NAME" property="userName" />
			<result column="PASSWORD" property="password" />
			<result column="NAME" property="name" />
			<result column="AGE" property="age" />
			<result column="EMAIL" property="email" />
			<result column="BIRTHDAY" property="birthday" />
			<result column="LRRQ" property="lrrq" />
			<result column="LRR_DM" property="lrrDm" />
			<result column="XGR_DM" property="xgrDm" />
			<result column="XGRQ" property="xgrq" />
			<result column="YXBZ" property="yxbz" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Table_Name">t_user</sql>
    <sql id="Base_Column_List_Without_Id">
			USER_NAME ,
			PASSWORD ,
			NAME ,
			AGE ,
			EMAIL ,
			BIRTHDAY ,
			LRRQ ,
			LRR_DM ,
			XGR_DM ,
			XGRQ ,
			YXBZ 
    </sql>
    <sql id="Base_Column_List">
			ID ,
        <include refid="Base_Column_List_Without_Id"/>
    </sql>
    <sql id="Insert_Columns">
			<if test="record.userName != null">USER_NAME,</if>
			<if test="record.password != null">PASSWORD,</if>
			<if test="record.name != null">NAME,</if>
			<if test="record.age != null">AGE,</if>
			<if test="record.email != null">EMAIL,</if>
			<if test="record.birthday != null">BIRTHDAY,</if>
			<if test="record.lrrq != null">LRRQ,</if>
			<if test="record.lrrDm != null">LRR_DM,</if>
			<if test="record.xgrDm != null">XGR_DM,</if>
			<if test="record.xgrq != null">XGRQ,</if>
			<if test="record.yxbz != null">YXBZ,</if>
    </sql>
    <sql id="Insert_Values">
			<if test="record.userName != null">#{record.userName},</if>
			<if test="record.password != null">#{record.password},</if>
			<if test="record.name != null">#{record.name},</if>
			<if test="record.age != null">#{record.age},</if>
			<if test="record.email != null">#{record.email},</if>
			<if test="record.birthday != null">#{record.birthday},</if>
			<if test="record.lrrq != null">#{record.lrrq},</if>
			<if test="record.lrrDm != null">#{record.lrrDm},</if>
			<if test="record.xgrDm != null">#{record.xgrDm},</if>
			<if test="record.xgrq != null">#{record.xgrq},</if>
			<if test="record.yxbz != null">#{record.yxbz},</if>
    </sql>
    <sql id="Batch_Insert_Values">
				#{record.userName},
				#{record.password},
				#{record.name},
				#{record.age},
				#{record.email},
				#{record.birthday},
				#{record.lrrq},
				#{record.lrrDm},
				#{record.xgrDm},
				#{record.xgrq},
				#{record.yxbz},
    </sql>
    <sql id="Update_Set_From_Bean">
			<if test="record.userName != null">USER_NAME = #{record.userName} ,</if>
			<if test="record.password != null">PASSWORD = #{record.password} ,</if>
			<if test="record.name != null">NAME = #{record.name} ,</if>
			<if test="record.age != null">AGE = #{record.age} ,</if>
			<if test="record.email != null">EMAIL = #{record.email} ,</if>
			<if test="record.birthday != null">BIRTHDAY = #{record.birthday} ,</if>
			<if test="record.lrrq != null">LRRQ = #{record.lrrq} ,</if>
			<if test="record.lrrDm != null">LRR_DM = #{record.lrrDm} ,</if>
			<if test="record.xgrDm != null">XGR_DM = #{record.xgrDm} ,</if>
			<if test="record.xgrq != null">XGRQ = #{record.xgrq} ,</if>
			<if test="record.yxbz != null">YXBZ = #{record.yxbz} ,</if>
    </sql>
    <!-- insert -->
    <insert id="insert" parameterType="java.util.Map">
			<selectKey resultType="java.lang.Long" keyProperty="record.id" order="AFTER">
				SELECT LAST_INSERT_ID()
			</selectKey>
        insert into
        <include refid="Table_Name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <include refid="Insert_Columns"/>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <include refid="Insert_Values"/>
        </trim>
    </insert>
    <insert id="batchInsert" parameterType="java.util.Map">
        insert into
        <include refid="Table_Name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <include refid="Base_Column_List_Without_Id"/>
        </trim>
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="Batch_Insert_Values"/>
            </trim>
        </foreach>
    </insert>
    <!-- end insert -->
    <!-- delete -->
    <delete id="deleteById" parameterType="java.util.Map">
        delete from
        <include refid="Table_Name"/>
			where ID = #{id}
    </delete>
    <delete id="deleteByExample" parameterType="java.util.Map">
        delete from
        <include refid="Table_Name"/>
        <if test="example != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <delete id="deleteIn" parameterType="java.util.Map">
        delete from
        <include refid="Table_Name"/>
			where ID in
        <foreach collection="records" item="record" index="index" open="(" separator="," close=")">
				#{record.id}
        </foreach>
    </delete>
    <!-- end delete -->
    <!-- update -->
    <update id="updateById" parameterType="java.util.Map">
        update
        <include refid="Table_Name"/>
        <set>
            <include refid="Update_Set_From_Bean"/>
        </set>
			where ID = #{record.id}
    </update>
    <update id="updateByExample" parameterType="java.util.Map">
        update
        <include refid="Table_Name"/>
        <set>
            <include refid="Update_Set_From_Bean"/>
        </set>
        <if test="example != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="batchUpdate" parameterType="java.util.Map">
        <foreach collection="records" item="record" index="index" open="" close="" separator=";">
            update
            <include refid="Table_Name"/>
            <set>
                <include refid="Update_Set_From_Bean"/>
            </set>
				where ID = #{record.id}
        </foreach>
    </update>
    <!-- end update -->
    <!-- select -->
    <select id="selectById" resultMap="UserMap" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
			where ID = #{id}
    </select>
    <select id="selectByExample" resultMap="UserMap" parameterType="java.util.Map">
        select
        <if test="example != null and example.distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        <if test="example != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="example != null and example.orderByClause != null">
            order by ${example.orderByClause}
        </if>
    </select>
    <select id="selectOneByExample" resultMap="UserMap" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        <if test="example != null">
            <include refid="Example_Where_Clause"/>
        </if>
        limit 1
    </select>
    <select id="selectIn" resultMap="UserMap" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
			where ID in
        <foreach collection="records" item="record" index="index" open="(" separator="," close=")">
				#{record.id}
        </foreach>
    </select>
    <select id="countByExample" resultType="java.lang.Integer" parameterType="java.util.Map">
        select count(*) as total from
        <include refid="Table_Name"/>
        <if test="example != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <select id="selectByPager" resultMap="UserMap" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        <if test="example != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="pager.sortItem != null and pager.sortItem != '' ">
            order by ${pager.sortItem} ${pager.sortType}
        </if>
    </select>
    <!-- end select -->
    <!-- My Custom Interfaces -->
</mapper>
