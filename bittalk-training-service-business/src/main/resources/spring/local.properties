app.code=bittalk-training-service

logger.root = info
logger.mapper = debug
logger.parameterLog = debug

#db
servyou.db.druid.demo.type = MYSQL
servyou.db.druid.demo.url = ***********************************************************************************
servyou.db.druid.demo.username = kf-template
#servyou.db.druid.demo.url = *****************************************************************************
#servyou.db.druid.demo.username = root
#servyou.db.druid.demo.password = 123456
servyou.db.druid.demo.password = Ls6D9jjMrdjQyzoZDwEf0w==
servyou.db.druid.demo.password.crypto = true
servyou.db.druid.demo.driverClassName = com.mysql.cj.jdbc.Driver
servyou.db.druid.demo.transactionManagerInit = true
servyou.db.druid.demo.sqlSessionFactoryInit = true
servyou.db.druid.demo.mapperScannerConfigurerInit = true
servyou.db.druid.demo.configLocation = classpath:mybatis/mybatis-config.xml
servyou.db.druid.demo.mapperLocations = classpath*:mybatis/mapper/*.xml
servyou.db.druid.demo.basePackage = gov.chinatax.apaas.**.mapper

#redis
servyou.cache.redis.enable = true
servyou.cache.redis.model = single
servyou.cache.redis.hostName = redis-test-apaas.dc.servyou-it.com
servyou.cache.redis.port = 6379
servyou.cache.redis.database = 1
servyou.cache.redis.usePool = true
servyou.cache.redis.password = Servyou2018
#servyou.cache.redis.encryptedPassword = vBtBM81/XR8L53/kqwcwAA==
servyou.cache.redis.defaultExpiration = 3600
servyou.cache.redis.redisPoolConfig.maxIdle = 30
servyou.cache.redis.redisPoolConfig.maxTotal = 300
servyou.cache.redis.redisPoolConfig.blockWhenExhausted = true
servyou.cache.redis.redisPoolConfig.maxWaitMillis = 3000
servyou.cache.redis.redisPoolConfig.testOnBorrow = true

#sse
sse.dubbo.application.name=bittalk-training-service
sse.dubbo.registry.address=nacos://nacos-test-apaas.dc.servyou-it.com:8851/nacos/?namespace=219d0310-115b-48c4-9542-0906163319de&username=nacos&password=nacos
sse.dubbo.protocol.name=dubbo
sse.dubbo.protocol.port=-1
sse.dubbo.protocol.threadpool=fixed
sse.dubbo.protocol.threads=200
sse.dubbo.protocol.iothreads=
sse.dubbo.protocol.accepts=
sse.dubbo.protocol.dispatcher=all
