<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>gov.chinatax.apaas</groupId>
        <artifactId>bittalk-training-service</artifactId>
        <version>1.0.0</version>
    </parent>

    <groupId>gov.chinatax.apaas</groupId>
    <artifactId>bittalk-training-service-jar</artifactId>
    <packaging>jar</packaging>
    <name>bittalk-training-service-jar</name>
    <description>bittalk-training-service-jar</description>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <java.version>1.8</java.version>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>gov.chinatax.apaas</groupId>
            <artifactId>bittalk-training-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>gov.chinatax.apaas</groupId>
            <artifactId>bittalk-training-service-business</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!-- test -->
        <dependency>
            <groupId>gov.chinatax.framework</groupId>
            <artifactId>template-module-generator-code</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>bittalk-training-service</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>gov.chinatax.framework</groupId>
                    <artifactId>common-middleware-cache-redis</artifactId>
                </dependency>
                <dependency>
                    <groupId>gov.chinatax.framework</groupId>
                    <artifactId>common-middleware-configuration-nacos</artifactId>
                </dependency>
                <dependency>
                    <groupId>gov.chinatax.framework</groupId>
                    <artifactId>common-middleware-mq-rocketmq</artifactId>
                </dependency>
                <dependency>
                    <groupId>gov.chinatax.framework</groupId>
                    <artifactId>common-middleware-sse-dubbo</artifactId>
                </dependency>
                <dependency>
                    <groupId>gov.chinatax.framework</groupId>
                    <artifactId>common-middleware-os-nfs</artifactId>
                </dependency>
                <dependency>
                    <groupId>gov.chinatax.framework</groupId>
                    <artifactId>common-middleware-scheduler-quartz</artifactId>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>aliyun</id>
            <dependencies>
                <dependency>
                    <groupId>gov.chinatax.framework</groupId>
                    <artifactId>common-middleware-cache-redis</artifactId>
                </dependency>
                <dependency>
                    <groupId>gov.chinatax.framework</groupId>
                    <artifactId>common-middleware-configuration-acm</artifactId>
                </dependency>
                <dependency>
                    <groupId>gov.chinatax.framework</groupId>
                    <artifactId>common-middleware-mq-ons</artifactId>
                </dependency>
                <dependency>
                    <groupId>gov.chinatax.framework</groupId>
                    <artifactId>common-middleware-sse-hsf</artifactId>
                </dependency>
                <dependency>
                    <groupId>gov.chinatax.framework</groupId>
                    <artifactId>common-middleware-os-oss</artifactId>
                </dependency>
                <dependency>
                    <groupId>gov.chinatax.framework</groupId>
                    <artifactId>common-middleware-scheduler-x</artifactId>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>tencent</id>
            <properties>
                <profiles.bootstrap.file>bootstrap-tsf.yaml</profiles.bootstrap.file>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>gov.chinatax.framework</groupId>
                    <artifactId>common-middleware-cache-redis</artifactId>
                </dependency>
                <dependency>
                    <groupId>gov.chinatax.framework</groupId>
                    <artifactId>common-middleware-configuration-tsf</artifactId>
                </dependency>
                <dependency>
                    <groupId>gov.chinatax.framework</groupId>
                    <artifactId>common-middleware-mq-pulsar</artifactId>
                </dependency>
                <dependency>
                    <groupId>gov.chinatax.framework</groupId>
                    <artifactId>common-middleware-tsf-starter</artifactId>
                </dependency>
                <dependency>
                    <groupId>gov.chinatax.framework</groupId>
                    <artifactId>common-middleware-sse-tsf</artifactId>
                </dependency>
                <dependency>
                    <groupId>gov.chinatax.framework</groupId>
                    <artifactId>common-middleware-os-cos</artifactId>
                </dependency>
                <dependency>
                    <groupId>gov.chinatax.framework</groupId>
                    <artifactId>common-middleware-scheduler-quartz</artifactId>
                </dependency>
            </dependencies>
        </profile>
    </profiles>

</project>
