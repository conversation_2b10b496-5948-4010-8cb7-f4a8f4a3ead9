package gov.chinatax.apaas.training;

import gov.chinatax.framework.template.generator.code.CodeGenerator;

public class Generator {

	public static void main(String[] args) throws Exception {
		// 模板应用存放路径
		String projectPath = System.getProperty("user.dir") + "/bittalk-training-service-war";
		// 基础包名
		String parentPackage = "gov.chinatax.apaas.training";
		// 作者
		String authorName = "sgl";
		// 数据库驱动包名
		String driverName = "com.mysql.jdbc.Driver";
		// 数据库连接
		String url = "***********************************************************************************";
		// 数据库用户名
		String username = "kf-template";
		// 数据库密码
		String password = "123456";
		// 表名
		String[] tables = {"t_user"};
		// 表前缀
		String[] prefix = {"t_"};
		CodeGenerator.generator(projectPath, parentPackage, authorName, driverName, url, username, password
				, tables, prefix);
	}

}
