# Goff专版镜像
# desc：https://console.cloud.tencent.com/tke2/registry/qcloud/default/detail/info?rid=1&reponame=goff/jdk8
FROM ccr.ccs.tencentyun.com/goff/jdk8

MAINTAINER hassan

ENV jar docker_run.jar
WORKDIR /app/

# 命名jar包
ADD ./bittalk-training-service-jar/target/bittalk-training-service-jar.jar /app/${jar}
# 下载相关工具，可以按需添加
# RUN wget -P /usr/local/ http://abc.com

# 请在镜像运行环境定义JVM内存参数JAVA_OPTS，否则默认内存512m
# ex：export JAVA_OPTS='-Xms512m -Xmx512m -XX:NewRatio=1 -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m'
CMD sh -c ' if [ "$JAVA_OPTS" = "" ];  then export JAVA_OPTS="-Xms512m -Xmx512m -XX:NewRatio=1 -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m" ; else echo JAVA_OPTS is ${JAVA_OPTS} ; fi  \
     && exec java ${JAVA_OPTS}  -jar ${jar} '