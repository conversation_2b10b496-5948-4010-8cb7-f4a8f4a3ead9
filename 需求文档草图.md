# 培训服务系统需求文档

## 1. 管理端：运营

### 1.1 付费模式管理
配置付费模式及具体规则

#### 1.1.1 产品功能范围
- 定义不同付费模式下的功能权限
- 配置功能模块的开放程度
- 设置功能使用限制

#### 1.1.2 数据服务范围
- **问答数量限制**：设置不同模式下的问答数量上限
- **试卷数量限制**：配置试卷创建和使用数量
- **定制化服务**：是否支持个性化定制
- **更新频率**：内容更新的频率设置

#### 1.1.3 资源配置规则
- **账号授权数**：设置可授权的账号数量
- **同时在线数**：配置同时使用系统的用户数限制
- **评卷时长**：设置自动评卷的时间限制

### 1.2 租户管理
配置租户基础信息

- **税局**：税务局机构信息管理
- **学校**：教育机构信息管理
- **远程中心**：远程培训中心信息管理

### 1.3 租户服务管理
配置租户的服务标准

#### 1.3.1 选择付费模式
- 为租户分配合适的付费模式
- 调整服务等级

#### 1.3.2 修改服务范围
- 动态调整服务内容
- 扩展或限制功能权限

#### 1.3.3 查看课程、试卷关联情况
- 查看租户关联的课程资源
- 监控试卷使用情况
- 分析资源利用率

### 1.4 机构管理
配置租户下的机构信息

#### 1.4.1 部门
- 部门层级结构管理
- 部门权限配置

#### 1.4.2 小组
- 小组创建与管理
- 小组成员分配

#### 1.4.3 班级
- 班级信息维护
- 班级学员管理

### 1.5 角色管理
配置角色及功能权限

- **运营**：系统运营管理权限
- **老师**：教学管理权限
- **学生**：学习功能权限
- **坐席**：客服支持权限
- **税局领导**：监督查看权限

### 1.6 人员管理
配置机构下的人员信息

- 人员基础信息录入
- 人员角色分配
- 人员权限管理

### 1.7 知识管理
管理知识库

- 知识内容创建与编辑
- 知识分类管理
- 知识版本控制

### 1.8 题库管理
配置题目信息

- **选择知识**：关联相关知识点
- **录入答案要点**：设置标准答案和评分要点
- **设置标签**：题目分类和标签管理

### 1.9 试卷管理
配置试卷信息

- **选择题目**：从题库中选择合适题目
- 试卷结构设计
- 评分规则设置

### 1.10 实训课程管理
配置课程信息

- **关联租户**：设置课程可见范围（全部或指定租户）
- **关联试卷**：绑定相应的测试试卷
- 课程内容管理

### 1.11 疑义管理
跟进异常反馈

- 疑义问题收集
- 问题处理流程
- 反馈结果通知

## 2. 管理端：学校老师

### 2.1 实训课程管理
查看课程及试卷信息

- 课程内容浏览
- 试卷详情查看
- 课程使用统计

### 2.2 实训概览
查看实训数据

- 学员参与情况统计
- 实训完成率分析
- 学习效果评估

### 2.3 考试管理
配置和查看考试信息

- **选择试卷**：从可用试卷中选择
- **选择考试人**：指定参考学员
- **开始考试**：启动考试流程
- **查看考试看板**：实时监控考试进度
- **查看考试结果**：分析考试成绩

### 2.4 成绩复核
复核考试成绩

- 成绩异议处理
- 重新评分
- 成绩调整记录

## 3. 用户端：学生

### 3.1 实战训练
进行实训

- **答题**：完成训练题目
- **查看解析**：学习答案解析
- **退出实训**：保存进度并退出
- **提交试卷**：完成训练提交
- **查看实训结果**：查看训练成绩和分析
- **疑义反馈**：对题目或评分提出疑义

### 3.2 考试测评
进行考试

- **收到考试提醒**：接收考试通知
- **答题**：完成考试题目
- **提交试卷**：提交考试答案
- **查看考试成绩**：查看考试结果
- **疑义反馈**：对考试结果提出疑义

### 3.3 个人面板
个人学习管理

- **查看历史考试成绩**：历史考试记录查询
- **查看历史训练成绩**：训练记录统计
- **查看错题**：错题集管理
- **重新练习**：针对错题重新练习
- **能力画像**：个人能力分析报告
- **消息通知**：系统消息和通知管理

