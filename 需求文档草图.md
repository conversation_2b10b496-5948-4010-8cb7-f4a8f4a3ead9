# 需求文档草图

## 1 管理端：运营

### 1.1 付费模式管理：配置付费模式及具体规则

#### 1.1.1 产品功能范围
#### 1.1.2 数据服务范围（问答数、试卷数、是否定制化、更新频率）
#### 1.1.3 资源配置规则（账号授权数、同时在使用数、评卷时长）

### 1.2 租户管理：配置租户基础信息：
税局
学校
远程中心

### 1.3 租户服务管理：配置租户的服务标准

#### 1.3.1 选择付费模式
#### 1.3.2 修改服务范围
#### 1.3.3 查看课程、试卷关联情况

### 1.4 机构管理：配置租户下的机构信息

部门
小组
班级

### 1.5 角色管理：配置角色及功能权限

- 运营
- 老师
- 学生
- 坐席
- 税局领导

### 1.6 人员管理
配置机构下的人员信息

### 1.7 知识管理
管理知识库

### 1.8 题库管理：配置题目信息

- 选择知识
- 录入答案要点
- 设置标签

### 1.9 试卷管理：配置试卷信息

- 选择题目

### 1.10 实训课程管理：配置课程信息

- 关联租户（全部或指定租户）
- 关联试卷

### 1.11 疑义管理
跟进异常反馈

## 2 管理端：学校老师

### 2.1 实训课程管理
查看课程及试卷信息

### 2.2 实训概览
查看实训数据

### 2.3 考试管理：配置和查看考试信息

- 选择试卷
- 选择考试人
- 开始考试
- 查看考试看板
- 查看考试结果

### 2.4 成绩复核
复核考试成绩

## 3 用户端：学生

### 3.1 实战训练：进行实训

- 答题
- 查看解析
- 退出实训
- 提交试卷
- 查看实训结果
- 疑义反馈

### 3.2 考试测评：进行考试

- 收到考试提醒
- 答题
- 提交试卷
- 查看考试成绩
- 疑义反馈

### 3.3 个人面板

- 查看历史考试成绩
- 查看历史训练成绩
- 查看错题
- 重新练习
- 能力画像
- 消息通知

